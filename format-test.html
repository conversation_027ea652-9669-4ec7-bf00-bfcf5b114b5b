<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste de Formatos - ICOnverter</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #000;
            color: #fff;
        }
        .test-container {
            border: 1px solid #333;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            background: #111;
        }
        .format-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .format-test {
            border: 1px solid #555;
            padding: 15px;
            border-radius: 8px;
            background: #222;
        }
        .status {
            padding: 5px 10px;
            border-radius: 4px;
            font-weight: bold;
            margin: 5px 0;
        }
        .success { background: #040; color: #afa; }
        .error { background: #400; color: #faa; }
        .warning { background: #440; color: #ffa; }
        .pending { background: #004; color: #aaf; }
        button {
            padding: 8px 16px;
            background: #00ff80;
            color: #000;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #00cc66; }
        button:disabled { background: #666; color: #999; cursor: not-allowed; }
        .details {
            font-size: 12px;
            margin-top: 10px;
            padding: 10px;
            background: #333;
            border-radius: 4px;
        }
        input[type="file"] {
            margin: 10px 0;
            padding: 10px;
            background: #333;
            color: #fff;
            border: 1px solid #555;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>Teste Abrangente de Formatos - ICOnverter</h1>
    
    <div class="test-container">
        <h2>Configuração do Teste</h2>
        <input type="file" id="testImage" accept="image/*">
        <button onclick="runAllTests()" id="runAllBtn" disabled>Executar Todos os Testes</button>
        <button onclick="clearResults()">Limpar Resultados</button>
        
        <div id="testProgress" style="margin-top: 10px;"></div>
    </div>

    <div class="format-grid" id="formatGrid">
        <!-- Os testes serão inseridos aqui dinamicamente -->
    </div>

    <script type="module">
        import { convertImageToFormat } from './src/utils/imageProcessing.ts';
        
        // Formatos a serem testados
        const formats = [
            { id: 'png', name: 'PNG', mimeType: 'image/png', confirmed: false, note: 'Formato padrão, sempre funciona' },
            { id: 'jpg', name: 'JPEG', mimeType: 'image/jpeg', confirmed: false, note: 'Suporte nativo do canvas' },
            { id: 'gif', name: 'GIF', mimeType: 'image/png', confirmed: false, note: 'Fallback para PNG (canvas não suporta GIF)' },
            { id: 'webp', name: 'WebP', mimeType: 'image/webp', confirmed: true, note: 'Suporte moderno' },
            { id: 'svg', name: 'SVG', mimeType: 'image/svg+xml', confirmed: true, note: 'Vetorização com imagetracerjs' },
            { id: 'ico', name: 'ICO', mimeType: 'image/x-icon', confirmed: true, note: 'Geração manual de ICO' },
            { id: 'tiff', name: 'TIFF', mimeType: 'image/png', confirmed: false, note: 'Fallback para PNG (canvas não suporta TIFF)' },
            { id: 'bmp', name: 'BMP', mimeType: 'image/bmp', confirmed: false, note: 'Suporte limitado do canvas' },
            { id: 'avif', name: 'AVIF', mimeType: 'image/avif', confirmed: false, note: 'Suporte moderno limitado' },
            { id: 'heic', name: 'HEIC', mimeType: 'image/png', confirmed: false, note: 'Fallback para PNG (canvas não suporta HEIC)' }
        ];

        let testImage = null;
        
        // Inicializar interface
        function initializeInterface() {
            const grid = document.getElementById('formatGrid');
            
            formats.forEach(format => {
                const div = document.createElement('div');
                div.className = 'format-test';
                div.innerHTML = `
                    <h3>${format.name} (${format.id.toUpperCase()})</h3>
                    <div class="status pending" id="status-${format.id}">
                        ${format.confirmed ? '✅ Confirmado' : '⏳ Aguardando teste'}
                    </div>
                    <p style="font-size: 11px; color: #999; margin: 5px 0;">${format.note}</p>
                    <button onclick="testFormat('${format.id}')" id="btn-${format.id}" disabled>
                        Testar ${format.name}
                    </button>
                    <div class="details" id="details-${format.id}" style="display: none;"></div>
                `;
                grid.appendChild(div);
            });
        }

        // Habilitar botões quando imagem for selecionada
        document.getElementById('testImage').addEventListener('change', function(e) {
            testImage = e.target.files[0];
            const hasImage = !!testImage;
            
            document.getElementById('runAllBtn').disabled = !hasImage;
            formats.forEach(format => {
                document.getElementById(`btn-${format.id}`).disabled = !hasImage;
            });
            
            if (hasImage) {
                document.getElementById('testProgress').innerHTML = `
                    <div class="status success">Imagem carregada: ${testImage.name} (${(testImage.size/1024).toFixed(1)} KB)</div>
                `;
            }
        });

        // Testar formato específico
        window.testFormat = async function(formatId) {
            if (!testImage) return;
            
            const format = formats.find(f => f.id === formatId);
            const statusEl = document.getElementById(`status-${formatId}`);
            const detailsEl = document.getElementById(`details-${formatId}`);
            const btnEl = document.getElementById(`btn-${formatId}`);
            
            statusEl.className = 'status pending';
            statusEl.textContent = '🔄 Testando...';
            btnEl.disabled = true;
            detailsEl.style.display = 'none';
            
            try {
                const startTime = Date.now();
                
                // Configuração específica para cada formato
                const config = formatId === 'ico' ? { ico: { maxSize: 48, selectedSizes: [16, 32, 48] } } :
                              formatId === 'webp' ? { webp: { quality: 90 } } :
                              formatId === 'svg' ? { svg: { preset: 'default', detail: 50 } } : {};
                
                const blob = await convertImageToFormat(testImage, formatId, 0.9, config);
                const endTime = Date.now();
                
                // Verificações
                const checks = {
                    blobCreated: !!blob,
                    correctMimeType: blob.type === format.mimeType,
                    hasContent: blob.size > 0,
                    reasonableSize: blob.size < 50 * 1024 * 1024, // 50MB limit
                    canCreateURL: false,
                    canDownload: false
                };
                
                // Testar criação de URL
                try {
                    const url = URL.createObjectURL(blob);
                    checks.canCreateURL = true;
                    URL.revokeObjectURL(url);
                } catch (e) {
                    console.error('URL creation failed:', e);
                }
                
                // Testar download
                try {
                    const url = URL.createObjectURL(blob);
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = `test.${formatId}`;
                    checks.canDownload = true;
                    URL.revokeObjectURL(url);
                } catch (e) {
                    console.error('Download test failed:', e);
                }
                
                const allPassed = Object.values(checks).every(check => check === true);
                
                statusEl.className = allPassed ? 'status success' : 'status warning';
                statusEl.textContent = allPassed ? '✅ Funcionando' : '⚠️ Problemas detectados';
                
                detailsEl.innerHTML = `
                    <strong>Resultados do teste:</strong><br>
                    ⏱️ Tempo: ${endTime - startTime}ms<br>
                    📦 Tamanho: ${(blob.size / 1024).toFixed(1)} KB<br>
                    🏷️ MIME Type: ${blob.type}<br>
                    ✅ Blob criado: ${checks.blobCreated ? 'Sim' : 'Não'}<br>
                    ✅ MIME correto: ${checks.correctMimeType ? 'Sim' : 'Não'}<br>
                    ✅ Tem conteúdo: ${checks.hasContent ? 'Sim' : 'Não'}<br>
                    ✅ Tamanho válido: ${checks.reasonableSize ? 'Sim' : 'Não'}<br>
                    ✅ URL criada: ${checks.canCreateURL ? 'Sim' : 'Não'}<br>
                    ✅ Download OK: ${checks.canDownload ? 'Sim' : 'Não'}
                `;
                detailsEl.style.display = 'block';
                
            } catch (error) {
                statusEl.className = 'status error';
                statusEl.textContent = '❌ Erro na conversão';
                
                detailsEl.innerHTML = `
                    <strong>Erro encontrado:</strong><br>
                    ${error.message}<br>
                    <details>
                        <summary>Stack trace</summary>
                        <pre>${error.stack}</pre>
                    </details>
                `;
                detailsEl.style.display = 'block';
            }
            
            btnEl.disabled = false;
        };

        // Executar todos os testes
        window.runAllTests = async function() {
            document.getElementById('testProgress').innerHTML = '<div class="status pending">🔄 Executando todos os testes...</div>';
            
            for (let i = 0; i < formats.length; i++) {
                const format = formats[i];
                document.getElementById('testProgress').innerHTML = `
                    <div class="status pending">🔄 Testando ${format.name} (${i + 1}/${formats.length})</div>
                `;
                await testFormat(format.id);
                await new Promise(resolve => setTimeout(resolve, 500)); // Pausa entre testes
            }
            
            document.getElementById('testProgress').innerHTML = '<div class="status success">✅ Todos os testes concluídos!</div>';
        };

        // Limpar resultados
        window.clearResults = function() {
            formats.forEach(format => {
                const statusEl = document.getElementById(`status-${format.id}`);
                const detailsEl = document.getElementById(`details-${format.id}`);
                
                statusEl.className = 'status pending';
                statusEl.textContent = format.confirmed ? '✅ Confirmado' : '⏳ Aguardando teste';
                detailsEl.style.display = 'none';
            });
            
            document.getElementById('testProgress').innerHTML = '';
        };

        // Inicializar quando a página carregar
        initializeInterface();
    </script>
</body>
</html>
