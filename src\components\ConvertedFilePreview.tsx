import { useState, useEffect } from 'react';
import { ArrowDownCircle, Image } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { generateThumbnail, supportsThumbnailGeneration, generateSimpleObjectUrl, revokeObjectUrl } from '../utils/thumbnailGenerator';

interface ConvertedFilePreviewProps {
  originalFile: File; // This is actually the converted file
  convertedFormat: string;
  onDownload: () => void;
}

export default function ConvertedFilePreview({
  originalFile, // This is actually the converted file
  convertedFormat,
  onDownload
}: ConvertedFilePreviewProps) {
  const { t } = useTranslation();
  const [thumbnailUrl, setThumbnailUrl] = useState<string | null>(null);
  const [thumbnailError, setThumbnailError] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  const fileName = originalFile.name.split('.')[0] + '.' + convertedFormat;

  // Generate thumbnail from the converted file
  useEffect(() => {
    let currentUrl: string | null = null;
    let isMounted = true;

    const createThumbnail = async () => {
      try {
        setIsLoading(true);
        setThumbnailError(false);

        // Clean up previous URL if exists
        if (thumbnailUrl) {
          revokeObjectUrl(thumbnailUrl);
          setThumbnailUrl(null);
        }

        // Check if the file supports thumbnail generation
        if (supportsThumbnailGeneration(originalFile)) {
          try {
            // Generate optimized thumbnail
            const thumbnailDataUrl = await generateThumbnail(originalFile, {
              maxWidth: 96,
              maxHeight: 96,
              quality: 0.8
            });

            if (isMounted) {
              setThumbnailUrl(thumbnailDataUrl);
              setIsLoading(false);
            }
          } catch (thumbnailError) {
            console.warn('Optimized thumbnail generation failed, falling back to simple method:', thumbnailError);

            // Fallback to simple object URL
            currentUrl = generateSimpleObjectUrl(originalFile);

            // Test if the image can be loaded
            const img = new Image();
            img.onload = () => {
              if (isMounted) {
                setThumbnailUrl(currentUrl);
                setIsLoading(false);
              }
            };
            img.onerror = () => {
              if (currentUrl) {
                revokeObjectUrl(currentUrl);
              }
              if (isMounted) {
                setThumbnailError(true);
                setIsLoading(false);
              }
            };
            img.src = currentUrl;
          }
        } else {
          // File type doesn't support thumbnails
          if (isMounted) {
            setThumbnailError(true);
            setIsLoading(false);
          }
        }

      } catch (error) {
        console.error('Error generating thumbnail:', error);
        if (isMounted) {
          setThumbnailError(true);
          setIsLoading(false);
        }
      }
    };

    createThumbnail();

    // Cleanup function
    return () => {
      isMounted = false;
      if (currentUrl) {
        revokeObjectUrl(currentUrl);
      }
    };
  }, [originalFile]);

  // Get fallback emoji for the format
  const getFormatEmoji = (format: string) => {
    const emojiMap: { [key: string]: string } = {
      ico: '🖼️',
      png: '🎨',
      jpg: '📷',
      jpeg: '📷',
      gif: '🎞️',
      webp: '🌐',
      svg: '✨',
      tiff: '📄',
      bmp: '🖥️',
      avif: '🚀',
      heic: '📱',
      raw: '📸'
    };
    return emojiMap[format.toLowerCase()] || '🖼️';
  };
  
  return (
    <div className="relative group rounded-lg overflow-hidden shadow-sm border border-dark-border bg-dark-surface">
      <div className="h-24 flex items-center justify-center bg-primary-50 p-1 relative overflow-hidden">
        {isLoading ? (
          // Loading state
          <div className="flex items-center justify-center">
            <div className="w-4 h-4 border-2 border-accent-500 border-t-transparent rounded-full animate-spin"></div>
          </div>
        ) : thumbnailUrl && !thumbnailError ? (
          // Show actual thumbnail
          <img
            src={thumbnailUrl}
            alt={fileName}
            className="max-w-full max-h-full object-contain rounded thumbnail-fade-in"
            onError={() => {
              setThumbnailError(true);
              if (thumbnailUrl && thumbnailUrl.startsWith('blob:')) {
                revokeObjectUrl(thumbnailUrl);
              }
            }}
          />
        ) : (
          // Fallback to emoji or icon
          <div className="flex items-center justify-center">
            {thumbnailError ? (
              <Image className="w-6 h-6 text-primary-400" />
            ) : (
              <div className="text-2xl">
                {getFormatEmoji(convertedFormat)}
              </div>
            )}
          </div>
        )}
      </div>
      <div className="p-2">
        <p className="text-xs font-medium text-primary-800 truncate" title={fileName}>
          {fileName}
        </p>
        <div className="flex justify-between items-center mt-1">
          <span className="text-xs bg-accent-500/20 text-accent-400 px-2 py-0.5 rounded-full">
            Convertido
          </span>
          <button
            onClick={onDownload}
            className="text-accent-500 hover:text-accent-400 transition-colors"
            aria-label="Download file"
          >
            <ArrowDownCircle className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  );
}