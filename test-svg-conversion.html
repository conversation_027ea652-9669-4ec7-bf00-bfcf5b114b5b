<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste de Conversão SVG</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #000;
            color: #fff;
        }
        .test-container {
            border: 1px solid #333;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            background: #111;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
            background: #222;
        }
        .error {
            background: #400;
            color: #faa;
        }
        .success {
            background: #040;
            color: #afa;
        }
        input[type="file"] {
            margin: 10px 0;
            padding: 10px;
            background: #333;
            color: #fff;
            border: 1px solid #555;
            border-radius: 4px;
        }
        button {
            padding: 10px 20px;
            background: #00ff80;
            color: #000;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
        }
        button:hover {
            background: #00cc66;
        }
        button:disabled {
            background: #666;
            color: #999;
            cursor: not-allowed;
        }
        .preview {
            max-width: 200px;
            max-height: 200px;
            border: 1px solid #555;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>Teste de Conversão SVG - ICOnverter</h1>
    
    <div class="test-container">
        <h2>Teste de Conversão Raster para SVG</h2>
        <p>Selecione uma imagem para testar a conversão para SVG:</p>
        
        <input type="file" id="imageInput" accept="image/*">
        <br>
        <button id="convertBtn" onclick="testSvgConversion()" disabled>Converter para SVG</button>
        
        <div id="result" class="result" style="display: none;"></div>
        <div id="preview" style="display: none;">
            <h3>Resultado:</h3>
            <div id="svgContainer"></div>
        </div>
    </div>

    <script type="module">
        // Import the conversion function
        import { convertImageToFormat } from './src/utils/imageProcessing.ts';
        
        // Make it available globally for the onclick handler
        window.convertImageToFormat = convertImageToFormat;
        
        // Enable button when file is selected
        document.getElementById('imageInput').addEventListener('change', function(e) {
            const btn = document.getElementById('convertBtn');
            btn.disabled = !e.target.files.length;
        });
        
        window.testSvgConversion = async function() {
            const input = document.getElementById('imageInput');
            const result = document.getElementById('result');
            const preview = document.getElementById('preview');
            const svgContainer = document.getElementById('svgContainer');
            
            if (!input.files.length) {
                result.className = 'result error';
                result.textContent = 'Por favor, selecione uma imagem primeiro.';
                result.style.display = 'block';
                return;
            }
            
            const file = input.files[0];
            
            try {
                result.className = 'result';
                result.textContent = 'Convertendo...';
                result.style.display = 'block';
                
                // Test SVG conversion
                const svgConfig = {
                    svg: {
                        preset: 'default',
                        detail: 50,
                        smoothing: 50
                    }
                };
                
                const svgBlob = await window.convertImageToFormat(file, 'svg', 0.9, svgConfig);
                
                // Read the SVG content
                const svgText = await svgBlob.text();
                
                // Display result
                result.className = 'result success';
                result.innerHTML = `
                    <strong>Conversão bem-sucedida!</strong><br>
                    Tamanho original: ${(file.size / 1024).toFixed(1)} KB<br>
                    Tamanho SVG: ${(svgBlob.size / 1024).toFixed(1)} KB<br>
                    Tipo MIME: ${svgBlob.type}
                `;
                
                // Show SVG preview
                svgContainer.innerHTML = svgText;
                preview.style.display = 'block';
                
            } catch (error) {
                result.className = 'result error';
                result.innerHTML = `
                    <strong>Erro na conversão:</strong><br>
                    ${error.message}
                `;
                preview.style.display = 'none';
                console.error('Erro detalhado:', error);
            }
        };
    </script>
</body>
</html>
