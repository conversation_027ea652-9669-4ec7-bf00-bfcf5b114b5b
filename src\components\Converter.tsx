import { useState, useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Loader2, Download, ArrowRight } from 'lucide-react';
import JSZip from 'jszip';
import FileUploader from './FileUploader';
import ConversionOptions, { ConversionConfig } from './ConversionOptions';
import FilePreview from './FilePreview';
import ConvertedFilePreview from './ConvertedFilePreview';
import { createPixelatedImage, convertImageToFormat } from '../utils/imageProcessing';

export default function Converter() {
  const { t } = useTranslation();
  const [files, setFiles] = useState<File[]>([]);
  const [selectedFormat, setSelectedFormat] = useState<string>('png');
  const [isConverting, setIsConverting] = useState<boolean>(false);
  const [convertedFiles, setConvertedFiles] = useState<File[]>([]);
  const [currentStep, setCurrentStep] = useState<number>(1);
  const [config, setConfig] = useState<ConversionConfig>({
    ico: { maxSize: 48, selectedSizes: [] },
    webp: { quality: 90 },
    svg: { preset: 'default', detail: 50, smoothing: 50 }
  });

  const handleFilesAccepted = useCallback((acceptedFiles: File[]) => {
    setFiles((prevFiles) => [...prevFiles, ...acceptedFiles]);
    setCurrentStep(2);
  }, []);

  const handleRemoveFile = useCallback((index: number) => {
    setFiles((prevFiles) => prevFiles.filter((_, i) => i !== index));
    setConvertedFiles((prevFiles) => prevFiles.filter((_, i) => i !== index));
    if (files.length === 1) {
      setCurrentStep(1);
      setConvertedFiles([]);
    }
  }, [files.length]);

  const handleConvert = useCallback(async () => {
    if (!files.length) return;

    setIsConverting(true);

    try {
      // Get quality based on format and config
      const getQuality = () => {
        if (selectedFormat === 'webp') {
          return (config.webp?.quality || 90) / 100;
        }
        return 0.9; // Default quality for other formats
      };

      const convertedBlobs = await Promise.all(
        files.map(file => convertImageToFormat(file, selectedFormat, getQuality(), config))
      );

      const convertedFiles = convertedBlobs.map((blob, index) => {
        const fileName = files[index].name.split('.')[0];

        // Use the actual MIME type from the blob, which is more accurate
        let mimeType = blob.type;

        // Fallback to format-based MIME type if blob doesn't have one
        if (!mimeType || mimeType === 'application/octet-stream') {
          switch (selectedFormat.toLowerCase()) {
            case 'ico':
              mimeType = 'image/x-icon';
              break;
            case 'svg':
              mimeType = 'image/svg+xml';
              break;
            case 'jpg':
            case 'jpeg':
              mimeType = 'image/jpeg';
              break;
            case 'gif':
              mimeType = 'image/gif';
              break;
            case 'webp':
              mimeType = 'image/webp';
              break;
            case 'avif':
              mimeType = 'image/avif';
              break;
            case 'bmp':
              mimeType = 'image/bmp';
              break;
            case 'tiff':
              mimeType = 'image/tiff';
              break;
            case 'heic':
            case 'heif':
              mimeType = 'image/heic';
              break;
            default:
              mimeType = 'image/png';
          }
        }

        return new File([blob], `${fileName}.${selectedFormat}`, {
          type: mimeType
        });
      });

      setConvertedFiles(convertedFiles);
      setCurrentStep(3);
    } catch (error) {
      console.error('Conversion error:', error);
      // You might want to show an error message to the user here
    } finally {
      setIsConverting(false);
    }
  }, [files, selectedFormat, config]);
  
  const handleDownload = useCallback((index: number) => {
    if (!convertedFiles[index]) return;

    const file = convertedFiles[index];
    const url = URL.createObjectURL(file);

    const link = document.createElement('a');
    link.href = url;
    link.download = file.name;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }, [convertedFiles]);

  const handleDownloadAll = useCallback(async () => {
    if (!convertedFiles.length) return;

    const zip = new JSZip();

    // Add all files to the zip
    convertedFiles.forEach((file) => {
      zip.file(file.name, file);
    });

    // Generate the zip file
    const content = await zip.generateAsync({ type: 'blob' });

    // Create download link
    const url = URL.createObjectURL(content);
    const link = document.createElement('a');
    link.href = url;
    link.download = `imagens_convertidas.zip`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }, [convertedFiles]);

  const resetConverter = () => {
    setFiles([]);
    setConvertedFiles([]);
    setCurrentStep(1);
  };

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="card card-normal text-center">
        <h1 className="text-2xl font-bold text-primary-900 mb-2">
          {t('hero.title')}
        </h1>
        <p className="text-sm text-primary-700">
          {t('hero.subtitle')}
        </p>
      </div>

      {/* Step Indicator */}
      <div className="flex items-center justify-center space-x-4 mb-6">
        <div className={`flex items-center space-x-2 ${currentStep >= 1 ? 'text-accent-500' : 'text-primary-500'}`}>
          <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${currentStep >= 1 ? 'bg-accent-500 text-dark-bg' : 'bg-primary-200 text-primary-600'}`}>
            1
          </div>
          <span className="text-xs font-medium">Upload</span>
        </div>
        <ArrowRight className="w-4 h-4 text-primary-500" />
        <div className={`flex items-center space-x-2 ${currentStep >= 2 ? 'text-accent-500' : 'text-primary-500'}`}>
          <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${currentStep >= 2 ? 'bg-accent-500 text-dark-bg' : 'bg-primary-200 text-primary-600'}`}>
            2
          </div>
          <span className="text-xs font-medium">Converter</span>
        </div>
        <ArrowRight className="w-4 h-4 text-primary-500" />
        <div className={`flex items-center space-x-2 ${currentStep >= 3 ? 'text-accent-500' : 'text-primary-500'}`}>
          <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${currentStep >= 3 ? 'bg-accent-500 text-dark-bg' : 'bg-primary-200 text-primary-600'}`}>
            3
          </div>
          <span className="text-xs font-medium">Download</span>
        </div>
      </div>

      {/* Step 1: Upload */}
      {currentStep === 1 && (
        <div className="card card-normal">
          <FileUploader onFilesAccepted={handleFilesAccepted} />
        </div>
      )}

      {/* Step 2: Select Format & Convert */}
      {currentStep === 2 && (
        <div className="space-y-4 animate-fade-in">
          <div className="card card-normal">
            <h3 className="text-lg font-semibold text-primary-900 mb-4">
              Arquivos Selecionados ({files.length})
            </h3>

            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3 mb-6">
              {files.map((file, index) => (
                <FilePreview
                  key={`${file.name}-${index}`}
                  file={file}
                  onRemove={() => handleRemoveFile(index)}
                />
              ))}
            </div>
          </div>

          <div className="card card-normal">
            <ConversionOptions
              selectedFormat={selectedFormat}
              onFormatChange={setSelectedFormat}
              config={config}
              onConfigChange={setConfig}
              files={files}
            />

            <div className="mt-6 flex justify-center space-x-3">
              <button
                className="btn btn-outline"
                onClick={resetConverter}
              >
                Voltar
              </button>
              <button
                className="btn btn-primary px-6"
                onClick={handleConvert}
                disabled={isConverting || files.length === 0}
              >
                {isConverting ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    {t('converter.processing')}
                  </>
                ) : (
                  t('converter.convertButton')
                )}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Step 3: Download */}
      {currentStep === 3 && convertedFiles.length > 0 && (
        <div className="card card-normal animate-fade-in">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold text-primary-900">
              Arquivos Convertidos
            </h3>
            <div className="flex space-x-2">
              <button
                className="btn btn-outline text-xs"
                onClick={resetConverter}
              >
                Novo
              </button>
              {convertedFiles.length > 1 && (
                <button
                  onClick={handleDownloadAll}
                  className="btn btn-primary flex items-center gap-2 text-xs"
                >
                  <Download className="w-3 h-3" />
                  Baixar Todos
                </button>
              )}
            </div>
          </div>

          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3">
            {convertedFiles.map((file, index) => (
              <ConvertedFilePreview
                key={`converted-${file.name}-${index}`}
                originalFile={file}
                convertedFormat={selectedFormat}
                onDownload={() => handleDownload(index)}
              />
            ))}
          </div>
        </div>
      )}
    </div>
  );
}