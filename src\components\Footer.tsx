import { useTranslation } from 'react-i18next';
import { Facebook, Twitter, Instagram } from 'lucide-react';

export default function Footer() {
  const { t } = useTranslation();
  
  return (
    <footer className="bg-primary-900 text-primary-100 mt-20">
      <div className="container-custom py-16">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div>
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-br from-accent-500 to-accent-600 rounded-xl flex items-center justify-center shadow-lg">
                <svg
                  viewBox="0 0 24 24"
                  className="w-6 h-6 text-white"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M21.9 8.5a2.8 2.8 0 0 0-2.03-2.65L7.13 2.1a2.8 2.8 0 0 0-3.53 1.65L2.1 7.13A2.8 2.8 0 0 0 3.75 10.7L5 11.27V19a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2v-7.73l1.24-.57A2.8 2.8 0 0 0 21.9 8.5z"/>
                  <path d="M7 16h.01"/>
                  <path d="M17 16h.01"/>
                  <path d="M11.25 12h1.5L14 16h-4l1.25-4z"/>
                </svg>
              </div>
              <span className="font-bold text-xl">ICOnverter</span>
            </div>
            <p className="mt-6 text-primary-300 leading-relaxed">
              Ferramenta gratuita para converter imagens entre diferentes formatos, de maneira fácil e rápida.
            </p>
            <div className="flex space-x-4 mt-8">
              <a href="#" className="text-primary-400 hover:text-accent-400 transition-colors p-2 rounded-lg hover:bg-primary-800">
                <Facebook className="w-5 h-5" />
              </a>
              <a href="#" className="text-primary-400 hover:text-accent-400 transition-colors p-2 rounded-lg hover:bg-primary-800">
                <Twitter className="w-5 h-5" />
              </a>
              <a href="#" className="text-primary-400 hover:text-accent-400 transition-colors p-2 rounded-lg hover:bg-primary-800">
                <Instagram className="w-5 h-5" />
              </a>
            </div>
          </div>
          
          <div>
            <h3 className="font-semibold text-lg mb-6 text-white">Links Rápidos</h3>
            <ul className="space-y-3">
              <li><a href="#" className="text-primary-300 hover:text-accent-400 transition-colors">Início</a></li>
              <li><a href="#" className="text-primary-300 hover:text-accent-400 transition-colors">Sobre Nós</a></li>
              <li><a href="#" className="text-primary-300 hover:text-accent-400 transition-colors">Contato</a></li>
              <li><a href="#" className="text-primary-300 hover:text-accent-400 transition-colors">FAQ</a></li>
            </ul>
          </div>

          <div>
            <h3 className="font-semibold text-lg mb-6 text-white">Formatos</h3>
            <ul className="space-y-3">
              <li><a href="#" className="text-primary-300 hover:text-accent-400 transition-colors">ICO</a></li>
              <li><a href="#" className="text-primary-300 hover:text-accent-400 transition-colors">PNG</a></li>
              <li><a href="#" className="text-primary-300 hover:text-accent-400 transition-colors">JPG</a></li>
              <li><a href="#" className="text-primary-300 hover:text-accent-400 transition-colors">TIFF</a></li>
              <li><a href="#" className="text-primary-300 hover:text-accent-400 transition-colors">BMP</a></li>
              <li><a href="#" className="text-primary-300 hover:text-accent-400 transition-colors">AVIF</a></li>
              <li><a href="#" className="text-primary-300 hover:text-accent-400 transition-colors">HEIC</a></li>
              <li><a href="#" className="text-primary-300 hover:text-accent-400 transition-colors">RAW</a></li>
            </ul>
          </div>

          <div>
            <h3 className="font-semibold text-lg mb-6 text-white">Legal</h3>
            <ul className="space-y-3">
              <li><a href="#" className="text-primary-300 hover:text-accent-400 transition-colors">{t('footer.terms')}</a></li>
              <li><a href="#" className="text-primary-300 hover:text-accent-400 transition-colors">{t('footer.privacy')}</a></li>
              <li><a href="#" className="text-primary-300 hover:text-accent-400 transition-colors">Cookies</a></li>
            </ul>
          </div>
        </div>
        
        <div className="border-t border-primary-800 mt-16 pt-8">
          <p className="text-center text-primary-400 text-sm">{t('footer.copyright')}</p>
        </div>
      </div>
    </footer>
  );
}