import { useEffect, useState } from 'react';
import { X, Image } from 'lucide-react';

interface FilePreviewProps {
  file: File;
  onRemove: () => void;
}

export default function FilePreview({ file, onRemove }: FilePreviewProps) {
  const [preview, setPreview] = useState<string | null>(null);

  useEffect(() => {
    if (!file) return;
    
    const reader = new FileReader();
    reader.onload = () => {
      setPreview(reader.result as string);
    };
    reader.readAsDataURL(file);

    return () => {
      // Revoke object URL when component unmounts to avoid memory leaks
      if (preview) URL.revokeObjectURL(preview);
    };
  }, [file]);

  if (!file || !preview) {
    return (
      <div className="flex items-center justify-center bg-primary-100 rounded-lg h-24 w-full">
        <Image className="w-6 h-6 text-primary-400" />
      </div>
    );
  }

  return (
    <div className="relative group rounded-lg overflow-hidden shadow-sm border border-dark-border bg-dark-surface">
      <img
        src={preview}
        alt={file.name}
        className="w-full h-24 object-contain bg-primary-50 p-1"
      />
      <div className="absolute inset-0 bg-black/0 group-hover:bg-black/50 transition-colors duration-200 flex items-center justify-center opacity-0 group-hover:opacity-100">
        <button
          onClick={onRemove}
          className="p-1 rounded-full bg-dark-surface text-primary-700 hover:text-red-400 transition-colors"
          aria-label="Remove file"
        >
          <X className="w-3 h-3" />
        </button>
      </div>
      <div className="p-2">
        <p className="text-xs font-medium text-primary-800 truncate" title={file.name}>
          {file.name}
        </p>
        <p className="text-xs text-primary-600">
          {Math.round(file.size / 1024)} KB
        </p>
      </div>
    </div>
  );
}