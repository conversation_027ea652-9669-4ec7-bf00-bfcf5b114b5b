// Test file to verify imagetracerjs import
// @ts-ignore
import ImageTracer from 'imagetracerjs';

console.log('ImageTracer imported:', ImageTracer);
console.log('ImageTracer methods:', Object.keys(ImageTracer));

// Test if the library is working
export function testImageTracer() {
  try {
    console.log('ImageTracer version:', ImageTracer.versionnumber);
    console.log('ImageTracer optionpresets:', Object.keys(ImageTracer.optionpresets || {}));
    return true;
  } catch (error) {
    console.error('Error testing ImageTracer:', error);
    return false;
  }
}
