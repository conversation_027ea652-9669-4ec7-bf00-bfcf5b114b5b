import { useCallback, useState, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { useDropzone } from 'react-dropzone';
import { Upload, Target } from 'lucide-react';
import { cn } from '../utils/cn';
import { validateImageFile, getAllSupportedExtensions, MAX_FILE_SIZE } from '../utils/fileValidation';

interface FileUploaderProps {
  onFilesAccepted: (files: File[]) => void;
}

export default function FileUploader({ onFilesAccepted }: FileUploaderProps) {
  const { t } = useTranslation();
  const [isDragActive, setIsDragActive] = useState(false);
  const [validationError, setValidationError] = useState<string | null>(null);
  const [showRipple, setShowRipple] = useState(false);
  const [ripplePosition, setRipplePosition] = useState({ x: 0, y: 0 });
  const [showRejectAnimation, setShowRejectAnimation] = useState(false);
  const dropAreaRef = useRef<HTMLDivElement>(null);

  const onDrop = useCallback((acceptedFiles: File[], rejectedFiles: any[]) => {
    setValidationError(null);
    setIsDragActive(false);
    setShowRipple(false);

    if (rejectedFiles?.length > 0) {
      setShowRejectAnimation(true);
      const firstRejected = rejectedFiles[0];
      if (firstRejected.errors?.length > 0) {
        const error = firstRejected.errors[0];
        if (error.code === 'file-too-large') {
          setValidationError(`Arquivo muito grande. Tamanho máximo: ${Math.round(MAX_FILE_SIZE / (1024 * 1024))}MB`);
        } else if (error.code === 'file-invalid-type') {
          setValidationError(t('converter.unsupportedFile'));
        } else {
          setValidationError('Erro na validação do arquivo.');
        }
      }
      setTimeout(() => setShowRejectAnimation(false), 500);
      return;
    }

    if (acceptedFiles?.length) {
      // Additional validation for each file
      const validFiles: File[] = [];

      for (const file of acceptedFiles) {
        const validation = validateImageFile(file);
        if (validation.isValid) {
          validFiles.push(file);
        } else {
          setShowRejectAnimation(true);
          setValidationError(validation.error || 'Arquivo inválido');
          setTimeout(() => setShowRejectAnimation(false), 500);
          return;
        }
      }

      if (validFiles.length > 0) {
        onFilesAccepted(validFiles);
      }
    }
  }, [onFilesAccepted, t]);

  // Handle drag enter with ripple effect
  const handleDragEnter = useCallback((event: any) => {
    setIsDragActive(true);
    setValidationError(null);
    setShowRejectAnimation(false);

    // Create ripple effect at cursor position
    if (dropAreaRef.current) {
      const rect = dropAreaRef.current.getBoundingClientRect();
      const x = event.clientX - rect.left;
      const y = event.clientY - rect.top;
      setRipplePosition({ x, y });
      setShowRipple(true);
    }
  }, []);

  const handleDragLeave = useCallback(() => {
    setIsDragActive(false);
    setShowRipple(false);
  }, []);

  const { getRootProps, getInputProps, isDragReject } = useDropzone({
    onDrop,
    accept: {
      'image/*': getAllSupportedExtensions()
    },
    maxSize: MAX_FILE_SIZE,
    onDragEnter: handleDragEnter,
    onDragLeave: handleDragLeave
  });

  // Reset ripple effect after animation
  useEffect(() => {
    if (showRipple) {
      const timer = setTimeout(() => setShowRipple(false), 1500);
      return () => clearTimeout(timer);
    }
  }, [showRipple]);

  return (
    <div
      ref={dropAreaRef}
      {...getRootProps()}
      className={cn(
        "drop-area cursor-pointer bg-dark-surface focus:outline-none border-dark-border",
        isDragActive && "drop-area-active",
        (isDragReject || showRejectAnimation) && "drop-area-reject"
      )}
    >
      <input {...getInputProps()} />

      {/* Ripple Effect */}
      {showRipple && (
        <div
          className="drop-area-ripple"
          style={{
            left: ripplePosition.x - 50,
            top: ripplePosition.y - 50,
            width: 100,
            height: 100,
          }}
        />
      )}

      <div className="flex flex-col items-center justify-center text-center relative z-10">
        <div className={cn(
          "rounded-xl bg-gradient-to-br p-4 mb-4 shadow-lg transition-all duration-300",
          isDragActive
            ? "from-accent-500/30 to-accent-600/30 scale-110"
            : "from-accent-500/20 to-accent-600/20"
        )}>
          {isDragActive ? (
            <Target className={cn("w-8 h-8 text-accent-400 icon-upload icon-upload-active")} />
          ) : (
            <Upload className={cn("w-8 h-8 text-accent-500 icon-upload")} />
          )}
        </div>

        <h3 className={cn(
          "text-lg font-semibold mb-2 transition-all duration-300",
          isDragActive ? "text-accent-400 scale-105" : "text-primary-900"
        )}>
          {isDragActive
            ? t('converter.dropzoneActive')
            : t('converter.dragHere')}
        </h3>

        <p className={cn(
          "mb-3 text-sm transition-all duration-300",
          isDragActive ? "text-accent-300" : "text-primary-700"
        )}>
          {isDragActive ? t('converter.dropToUpload') : t('converter.orClick')}
        </p>

        <p className={cn(
          "text-xs px-3 py-1 rounded-lg transition-all duration-300",
          isDragActive
            ? "text-accent-300 bg-accent-500/20"
            : "text-primary-600 bg-primary-100"
        )}>
          {t('converter.uploadSubtitle')}
        </p>

        {(isDragReject || validationError) && (
          <p className="mt-3 text-xs text-red-400 bg-red-900/20 px-3 py-1 rounded-lg animate-pulse">
            {validationError || t('converter.unsupportedFile')}
          </p>
        )}
      </div>
    </div>
  );
}