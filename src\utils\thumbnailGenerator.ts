/**
 * Utility functions for generating optimized thumbnails from image files
 */

export interface ThumbnailOptions {
  maxWidth?: number;
  maxHeight?: number;
  quality?: number;
  format?: string;
}

/**
 * Generate an optimized thumbnail from a File blob
 * @param file - The image file to generate thumbnail from
 * @param options - Thumbnail generation options
 * @returns Promise<string> - Data URL of the generated thumbnail
 */
export async function generateThumbnail(
  file: File,
  options: ThumbnailOptions = {}
): Promise<string> {
  const {
    maxWidth = 96,
    maxHeight = 96,
    quality = 0.8,
    format = 'image/jpeg'
  } = options;

  return new Promise((resolve, reject) => {
    const img = new Image();
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');

    if (!ctx) {
      reject(new Error('Could not get canvas context'));
      return;
    }

    img.onload = () => {
      try {
        // Calculate new dimensions while maintaining aspect ratio
        const { width: newWidth, height: newHeight } = calculateDimensions(
          img.width,
          img.height,
          maxWidth,
          maxHeight
        );

        // Set canvas dimensions
        canvas.width = newWidth;
        canvas.height = newHeight;

        // Draw and resize the image
        ctx.drawImage(img, 0, 0, newWidth, newHeight);

        // Convert to data URL
        const dataUrl = canvas.toDataURL(format, quality);
        resolve(dataUrl);
      } catch (error) {
        reject(error);
      }
    };

    img.onerror = () => {
      reject(new Error('Failed to load image for thumbnail generation'));
    };

    // Create object URL and load the image
    const objectUrl = URL.createObjectURL(file);
    img.src = objectUrl;

    // Clean up object URL after image loads
    img.onload = () => {
      URL.revokeObjectURL(objectUrl);
      try {
        // Calculate new dimensions while maintaining aspect ratio
        const { width: newWidth, height: newHeight } = calculateDimensions(
          img.width,
          img.height,
          maxWidth,
          maxHeight
        );

        // Set canvas dimensions
        canvas.width = newWidth;
        canvas.height = newHeight;

        // Draw and resize the image
        ctx.drawImage(img, 0, 0, newWidth, newHeight);

        // Convert to data URL
        const dataUrl = canvas.toDataURL(format, quality);
        resolve(dataUrl);
      } catch (error) {
        reject(error);
      }
    };
  });
}

/**
 * Calculate new dimensions while maintaining aspect ratio
 * @param originalWidth - Original image width
 * @param originalHeight - Original image height
 * @param maxWidth - Maximum allowed width
 * @param maxHeight - Maximum allowed height
 * @returns Object with new width and height
 */
function calculateDimensions(
  originalWidth: number,
  originalHeight: number,
  maxWidth: number,
  maxHeight: number
): { width: number; height: number } {
  let newWidth = originalWidth;
  let newHeight = originalHeight;

  // Calculate scaling factor
  const widthRatio = maxWidth / originalWidth;
  const heightRatio = maxHeight / originalHeight;
  const scalingFactor = Math.min(widthRatio, heightRatio, 1); // Don't upscale

  newWidth = Math.round(originalWidth * scalingFactor);
  newHeight = Math.round(originalHeight * scalingFactor);

  return { width: newWidth, height: newHeight };
}

/**
 * Check if a file type supports thumbnail generation
 * @param file - The file to check
 * @returns boolean - Whether thumbnail generation is supported
 */
export function supportsThumbnailGeneration(file: File): boolean {
  const supportedTypes = [
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/gif',
    'image/webp',
    'image/bmp',
    'image/tiff',
    'image/avif'
  ];

  return supportedTypes.includes(file.type) || file.type.startsWith('image/');
}

/**
 * Generate a simple object URL for direct display (fallback method)
 * @param file - The image file
 * @returns string - Object URL
 */
export function generateSimpleObjectUrl(file: File): string {
  return URL.createObjectURL(file);
}

/**
 * Revoke an object URL to free memory
 * @param url - The object URL to revoke
 */
export function revokeObjectUrl(url: string): void {
  try {
    URL.revokeObjectURL(url);
  } catch (error) {
    console.warn('Failed to revoke object URL:', error);
  }
}
