import { isRawFormat } from './fileValidation';
// @ts-ignore - imagetracerjs doesn't have TypeScript definitions
import ImageTracer from 'imagetracerjs';

export async function createPixelatedImage(
  originalImage: File,
  scale: number = 8
): Promise<Blob> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    const url = URL.createObjectURL(originalImage);

    img.onload = () => {
      try {
        // Create canvas for the original size
        const originalCanvas = document.createElement('canvas');
        const octx = originalCanvas.getContext('2d')!;
        originalCanvas.width = img.width;
        originalCanvas.height = img.height;

        // Draw original image
        octx.imageSmoothingEnabled = false;
        octx.drawImage(img, 0, 0);

        // Create canvas for the scaled size
        const scaledCanvas = document.createElement('canvas');
        const sctx = scaledCanvas.getContext('2d')!;
        scaledCanvas.width = img.width * scale;
        scaledCanvas.height = img.height * scale;

        // Configure for pixel-perfect scaling
        sctx.imageSmoothingEnabled = false;

        // Draw scaled image
        sctx.drawImage(
          originalCanvas,
          0, 0,
          originalCanvas.width, originalCanvas.height,
          0, 0,
          scaledCanvas.width, scaledCanvas.height
        );

        // Convert to blob and resolve
        scaledCanvas.toBlob((blob) => {
          URL.revokeObjectURL(url);
          if (blob) {
            resolve(blob);
          } else {
            reject(new Error('Failed to create blob from canvas'));
          }
        }, 'image/png');
      } catch (error) {
        URL.revokeObjectURL(url);
        reject(error);
      }
    };

    img.onerror = () => {
      URL.revokeObjectURL(url);
      reject(new Error('Failed to load image'));
    };

    // For RAW files, we might need special handling
    if (isRawFormat(originalImage.name)) {
      // RAW files typically can't be displayed directly in browsers
      // For now, we'll create a placeholder
      reject(new Error('RAW files require special processing not yet implemented'));
    } else {
      img.src = url;
    }
  });
}

/**
 * Calculate pixel-perfect scaling for ICO conversion (like convertico.com)
 * Scales small pixel art images to larger sizes while maintaining blocky appearance
 */
function calculatePixelPerfectScale(width: number, height: number): {
  scale: number;
  targetWidth: number;
  targetHeight: number;
} {
  const maxDimension = Math.max(width, height);

  // For very small images (like 16x16), scale up significantly
  if (maxDimension <= 16) {
    const scale = maxDimension <= 8 ? 16 : 8; // 8x8 -> 128x128, 16x16 -> 128x128
    return {
      scale: scale,
      targetWidth: width * scale,
      targetHeight: height * scale
    };
  }

  // For small images (17-32px), scale to make them more visible
  if (maxDimension <= 32) {
    const scale = 4; // 32x32 becomes 128x128
    return {
      scale: scale,
      targetWidth: width * scale,
      targetHeight: height * scale
    };
  }

  // For medium images (33-64px), scale moderately
  if (maxDimension <= 64) {
    const scale = 2; // 64x64 becomes 128x128
    return {
      scale: scale,
      targetWidth: width * scale,
      targetHeight: height * scale
    };
  }

  // For larger images, keep original size but still apply pixel-perfect rendering
  return {
    scale: 1,
    targetWidth: width,
    targetHeight: height
  };
}

/**
 * Create pixel-perfect canvas with nearest-neighbor scaling
 */
function createPixelPerfectCanvas(
  img: HTMLImageElement,
  targetWidth: number,
  targetHeight: number
): HTMLCanvasElement {
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d')!;

  canvas.width = targetWidth;
  canvas.height = targetHeight;

  // Configure for pixel-perfect rendering - disable ALL smoothing
  ctx.imageSmoothingEnabled = false;

  // Browser-specific settings to ensure no smoothing
  if ('webkitImageSmoothingEnabled' in ctx) {
    (ctx as any).webkitImageSmoothingEnabled = false;
  }
  if ('mozImageSmoothingEnabled' in ctx) {
    (ctx as any).mozImageSmoothingEnabled = false;
  }
  if ('msImageSmoothingEnabled' in ctx) {
    (ctx as any).msImageSmoothingEnabled = false;
  }

  // Draw with nearest-neighbor scaling
  ctx.drawImage(img, 0, 0, img.width, img.height, 0, 0, targetWidth, targetHeight);

  return canvas;
}

/**
 * Create a valid ICO file with user-specified sizes
 * This generates a proper Windows-compatible ICO file using manual ICO format creation
 */
async function createValidIcoFile(
  img: HTMLImageElement,
  maxSize: number = 48,
  selectedSizes?: number[]
): Promise<Blob> {
  let sizes: number[];

  if (selectedSizes && selectedSizes.length > 0) {
    // Use user-selected sizes
    sizes = selectedSizes.sort((a, b) => a - b);
    console.log('ICO: Using user-selected sizes:', sizes);
  } else {
    // Fallback: generate all sizes up to maxSize (no forced base sizes)
    const allSizes = [16, 32, 48, 64, 128, 256];
    sizes = allSizes.filter(size => size <= maxSize).sort((a, b) => a - b);
    console.log('ICO: Using default sizes up to', maxSize, ':', sizes);
  }

  const pngData: { size: number; data: Uint8Array }[] = [];

  // Create PNG data for each size
  for (const size of sizes) {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d')!;
    canvas.width = size;
    canvas.height = size;

    // Disable smoothing for pixel-perfect scaling
    ctx.imageSmoothingEnabled = false;

    // Draw the image scaled to the target size
    ctx.drawImage(img, 0, 0, size, size);

    // Convert canvas to PNG data
    const blob = await new Promise<Blob>((resolve, reject) => {
      canvas.toBlob((blob) => {
        if (blob) {
          resolve(blob);
        } else {
          reject(new Error(`Failed to create ${size}x${size} PNG`));
        }
      }, 'image/png');
    });

    const arrayBuffer = await blob.arrayBuffer();
    const uint8Array = new Uint8Array(arrayBuffer);
    pngData.push({ size, data: uint8Array });
  }

  // Create ICO file manually
  return createIcoFromPngData(pngData);
}

/**
 * Create ICO file from PNG data arrays
 * Implements the ICO file format specification
 */
function createIcoFromPngData(pngData: { size: number; data: Uint8Array }[]): Blob {
  const numImages = pngData.length;

  // ICO header (6 bytes)
  const header = new Uint8Array(6);
  header[0] = 0; // Reserved
  header[1] = 0; // Reserved
  header[2] = 1; // Type (1 = ICO)
  header[3] = 0; // Type high byte
  header[4] = numImages; // Number of images
  header[5] = 0; // Number of images high byte

  // Directory entries (16 bytes each)
  const directorySize = numImages * 16;
  const directory = new Uint8Array(directorySize);

  let dataOffset = 6 + directorySize; // Start after header and directory

  for (let i = 0; i < numImages; i++) {
    const entry = pngData[i];
    const entryOffset = i * 16;

    directory[entryOffset + 0] = entry.size === 256 ? 0 : entry.size; // Width (0 = 256)
    directory[entryOffset + 1] = entry.size === 256 ? 0 : entry.size; // Height (0 = 256)
    directory[entryOffset + 2] = 0; // Color palette (0 = no palette)
    directory[entryOffset + 3] = 0; // Reserved
    directory[entryOffset + 4] = 1; // Color planes (low byte)
    directory[entryOffset + 5] = 0; // Color planes (high byte)
    directory[entryOffset + 6] = 32; // Bits per pixel (low byte)
    directory[entryOffset + 7] = 0; // Bits per pixel (high byte)

    // Data size (4 bytes, little endian)
    const dataSize = entry.data.length;
    directory[entryOffset + 8] = dataSize & 0xFF;
    directory[entryOffset + 9] = (dataSize >> 8) & 0xFF;
    directory[entryOffset + 10] = (dataSize >> 16) & 0xFF;
    directory[entryOffset + 11] = (dataSize >> 24) & 0xFF;

    // Data offset (4 bytes, little endian)
    directory[entryOffset + 12] = dataOffset & 0xFF;
    directory[entryOffset + 13] = (dataOffset >> 8) & 0xFF;
    directory[entryOffset + 14] = (dataOffset >> 16) & 0xFF;
    directory[entryOffset + 15] = (dataOffset >> 24) & 0xFF;

    dataOffset += dataSize;
  }

  // Combine all parts
  const totalSize = 6 + directorySize + pngData.reduce((sum, entry) => sum + entry.data.length, 0);
  const icoData = new Uint8Array(totalSize);

  // Copy header
  icoData.set(header, 0);

  // Copy directory
  icoData.set(directory, 6);

  // Copy PNG data
  let currentOffset = 6 + directorySize;
  for (const entry of pngData) {
    icoData.set(entry.data, currentOffset);
    currentOffset += entry.data.length;
  }

  return new Blob([icoData], { type: 'image/x-icon' });
}

/**
 * Convert raster image to SVG using vectorization
 * Uses imagetracerjs library to trace bitmap images into vector graphics
 */
async function convertToSVG(
  img: HTMLImageElement,
  config?: {
    preset?: string;
    ltres?: number;
    qtres?: number;
    scale?: number;
    strokewidth?: number;
  }
): Promise<Blob> {
  return new Promise((resolve, reject) => {
    try {
      // Create canvas to get ImageData
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d')!;
      canvas.width = img.width;
      canvas.height = img.height;

      // Draw image to canvas
      ctx.drawImage(img, 0, 0);

      // Get ImageData for imagetracerjs
      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);

      // Map detail level (10-100) to line threshold (0.1-2.0)
      // Lower ltres = more detail, so we invert the mapping
      const detailLevel = config?.detail || 50;
      const ltres = 2.1 - (detailLevel / 100) * 2.0; // Maps 10->2.0, 100->0.1

      // Configure vectorization options
      const options = {
        // Line threshold - controls level of detail
        ltres: ltres,
        // Quad threshold - controls curve smoothness
        qtres: 1,
        // Scale factor
        scale: 1,
        // Stroke width
        strokewidth: 1,
        // Optimize for web
        desc: false,
        // Use path elements instead of polygons for better compatibility
        pathomit: 8,
        // Color quantization - reduce colors for cleaner vectors
        colorsampling: 1,
        numberofcolors: 16,
        // Smoothing
        blurradius: 0,
        blurdelta: 20
      };

      // Apply preset if specified
      if (config?.preset && config.preset !== 'default') {
        // Use imagetracerjs preset from optionpresets
        if (ImageTracer.optionpresets && ImageTracer.optionpresets[config.preset]) {
          const presetOptions = ImageTracer.optionpresets[config.preset];
          Object.assign(options, presetOptions);
          // Override with our custom settings
          options.ltres = ltres;
        }
      }

      // Convert ImageData to SVG using imagetracerjs
      const svgString = ImageTracer.imagedataToSVG(imageData, options);

      // Create blob from SVG string
      const svgBlob = new Blob([svgString], { type: 'image/svg+xml' });
      resolve(svgBlob);

    } catch (error) {
      reject(new Error(`Failed to convert to SVG: ${error instanceof Error ? error.message : 'Unknown error'}`));
    }
  });
}

// Convert image to specific format
export async function convertImageToFormat(
  originalImage: File,
  targetFormat: string,
  quality: number = 0.9,
  config?: any
): Promise<Blob> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    const url = URL.createObjectURL(originalImage);

    img.onload = async () => {
      try {
        const isIcoFormat = targetFormat.toLowerCase() === 'ico' || targetFormat.toLowerCase() === 'icon';
        const isSvgFormat = targetFormat.toLowerCase() === 'svg';

        if (isIcoFormat) {
          // Create a valid ICO file with user-specified sizes
          const maxSize = config?.ico?.maxSize || 48;
          const selectedSizes = config?.ico?.selectedSizes;
          const icoBlob = await createValidIcoFile(img, maxSize, selectedSizes);
          URL.revokeObjectURL(url);
          resolve(icoBlob);
          return;
        }

        if (isSvgFormat) {
          // Convert raster image to SVG using vectorization
          const svgBlob = await convertToSVG(img, config?.svg);
          URL.revokeObjectURL(url);
          resolve(svgBlob);
          return;
        }

        // Standard conversion for other formats
        let canvas: HTMLCanvasElement;
        canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d')!;
        canvas.width = img.width;
        canvas.height = img.height;

        // Use default smoothing for other formats
        ctx.drawImage(img, 0, 0);

        // Determine MIME type
        let mimeType = 'image/png';
        switch (targetFormat.toLowerCase()) {
          case 'jpg':
          case 'jpeg':
            mimeType = 'image/jpeg';
            break;
          case 'webp':
            mimeType = 'image/webp';
            break;
          case 'avif':
            mimeType = 'image/avif';
            break;
          case 'bmp':
            mimeType = 'image/bmp';
            break;
          case 'tiff':
            mimeType = 'image/tiff';
            break;
          case 'svg':
            mimeType = 'image/svg+xml';
            break;
          default:
            mimeType = 'image/png';
        }

        // Convert to blob
        canvas.toBlob((blob) => {
          URL.revokeObjectURL(url);
          if (blob) {
            resolve(blob);
          } else {
            reject(new Error(`Failed to convert to ${targetFormat}`));
          }
        }, mimeType, quality);
      } catch (error) {
        URL.revokeObjectURL(url);
        reject(error);
      }
    };

    img.onerror = () => {
      URL.revokeObjectURL(url);
      reject(new Error('Failed to load image for conversion'));
    };

    img.src = url;
  });
}

/**
 * Create a pixel-perfect scaled version for demonstration/testing
 * This replicates the convertico.com behavior exactly
 * @param originalImage - Source image file
 * @param customScale - Optional custom scale factor
 * @returns Promise<Blob> - Pixel-perfect scaled image
 */
export async function createPixelPerfectDemo(
  originalImage: File,
  customScale?: number
): Promise<Blob> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    const url = URL.createObjectURL(originalImage);

    img.onload = () => {
      try {
        let scale: number;
        let targetWidth: number;
        let targetHeight: number;

        if (customScale) {
          // Use custom scale if provided
          scale = customScale;
          targetWidth = img.width * scale;
          targetHeight = img.height * scale;
        } else {
          // Use automatic scaling logic
          const scaleInfo = calculatePixelPerfectScale(img.width, img.height);
          scale = scaleInfo.scale;
          targetWidth = scaleInfo.targetWidth;
          targetHeight = scaleInfo.targetHeight;
        }

        const canvas = createPixelPerfectCanvas(img, targetWidth, targetHeight);

        // Convert to blob
        canvas.toBlob((blob) => {
          URL.revokeObjectURL(url);
          if (blob) {
            resolve(blob);
          } else {
            reject(new Error('Failed to create pixel-perfect demo'));
          }
        }, 'image/png');
      } catch (error) {
        URL.revokeObjectURL(url);
        reject(error);
      }
    };

    img.onerror = () => {
      URL.revokeObjectURL(url);
      reject(new Error('Failed to load image for pixel-perfect demo'));
    };

    img.src = url;
  });
}