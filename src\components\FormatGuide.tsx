export default function FormatGuide() {
  const formats = [
    {
      id: 'ico',
      name: 'ICO',
      description: 'Formato de ícone usado principalmente para favicons de websites e ícones de aplicações Windows.',
      useCases: ['Favicons', 'Ícones de aplicação Windows'],
      icon: '🖼️'
    },
    {
      id: 'png',
      name: 'PNG',
      description: 'Formato de imagem sem perda que suporta transparência, ideal para gráficos e imagens com detalhes.',
      useCases: ['Gráficos', 'Logos', 'Imagens com transparência'],
      icon: '🎨'
    },
    {
      id: 'jpg',
      name: 'JPEG',
      description: 'Formato com compressão que funciona melhor para fotografias e imagens com muitas cores.',
      useCases: ['Fotografias', 'Imagens para web', 'Imagens coloridas'],
      icon: '📷'
    },
    {
      id: 'gif',
      name: 'GIF',
      description: 'Formato que suporta animações simples e transparência limitada.',
      useCases: ['Animações', 'Memes', 'Ícones animados'],
      icon: '🎞️'
    },
    {
      id: 'webp',
      name: 'WebP',
      description: 'Formato moderno com excelente compressão, mantendo a qualidade e suportando transparência.',
      useCases: ['Imagens para web', 'Aplicativos móveis'],
      icon: '🌐'
    },
    {
      id: 'svg',
      name: 'SVG',
      description: 'Formato vetorial que mantém a qualidade em qualquer escala, ideal para gráficos e ícones.',
      useCases: ['Ícones', 'Logos', 'Ilustrações'],
      icon: '✨'
    },
    {
      id: 'tiff',
      name: 'TIFF',
      description: 'Formato de alta qualidade usado em impressão profissional e arquivamento de imagens.',
      useCases: ['Impressão profissional', 'Arquivamento', 'Fotografia'],
      icon: '📄'
    },
    {
      id: 'bmp',
      name: 'BMP',
      description: 'Formato bitmap simples e sem compressão, compatível com sistemas Windows.',
      useCases: ['Compatibilidade Windows', 'Imagens simples'],
      icon: '🖥️'
    },
    {
      id: 'avif',
      name: 'AVIF',
      description: 'Formato moderno com excelente compressão e qualidade, sucessor do WebP.',
      useCases: ['Web moderna', 'Aplicativos', 'Streaming'],
      icon: '🚀'
    },
    {
      id: 'heic',
      name: 'HEIC/HEIF',
      description: 'Formato usado por dispositivos Apple, oferece melhor compressão que JPEG.',
      useCases: ['Dispositivos Apple', 'Fotos móveis'],
      icon: '📱'
    },
    {
      id: 'raw',
      name: 'RAW',
      description: 'Formatos de arquivo bruto de câmeras profissionais (CR2, NEF, ARW).',
      useCases: ['Fotografia profissional', 'Edição avançada'],
      icon: '📸'
    }
  ];

  return (
    <div id="formats" className="container-custom py-16">
      <div className="max-w-3xl mx-auto text-center mb-12">
        <h2 className="text-3xl font-bold text-gray-900 mb-4">
          Formatos Suportados
        </h2>
        <p className="text-lg text-gray-600">
          Conheça os diferentes formatos e suas melhores aplicações
        </p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {formats.map((format) => (
          <div 
            key={format.id}
            className="bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow duration-200"
          >
            <div className="bg-gray-50 p-4 flex items-center">
              <div className="text-4xl mr-3">{format.icon}</div>
              <div>
                <h3 className="text-xl font-semibold text-gray-900">
                  {format.name}
                </h3>
                <p className="text-xs text-gray-500">
                  {format.useCases.join(' • ')}
                </p>
              </div>
            </div>
            <div className="p-4">
              <p className="text-gray-600">
                {format.description}
              </p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}