/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#1a1a1a',
          100: '#2a2a2a',
          200: '#3a3a3a',
          300: '#4a4a4a',
          400: '#6a6a6a',
          500: '#8a8a8a',
          600: '#aaaaaa',
          700: '#cccccc',
          800: '#e0e0e0',
          900: '#ffffff',
        },
        accent: {
          50: '#001a0d',
          100: '#003d1a',
          200: '#006633',
          300: '#00994d',
          400: '#00cc66',
          500: '#00ff80',
          600: '#33ff99',
          700: '#66ffb3',
          800: '#99ffcc',
          900: '#ccffe6',
        },
        dark: {
          bg: '#000000',
          surface: '#111111',
          border: '#333333',
        }
      },
      animation: {
        'fade-in': 'fadeIn 0.3s ease-in-out',
        'slide-up': 'slideUp 0.4s ease-out',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(20px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
      },
    },
  },
  plugins: [],
};